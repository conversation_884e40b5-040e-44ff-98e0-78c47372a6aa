[2025-07-20T01:20:34.357Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-20T01:20:34.412Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-20T01:20:34.413Z] [INFO] Queue service initialized successfully
[2025-07-20T01:20:34.413Z] [INFO] All services initialized successfully
[2025-07-20T01:20:34.417Z] [INFO] Assessment Service running on port 3003
[2025-07-20T01:20:34.418Z] [INFO] Environment: development
[2025-07-20T01:22:11.693Z] [WARN] Validation error {"source":"body","errors":{"viaIs.bravery":"VIA-IS assessment data is required","viaIs.perseverance":"VIA-IS assessment data is required","viaIs.honesty":"VIA-IS assessment data is required","viaIs.zest":"VIA-IS assessment data is required","viaIs.love":"VIA-IS assessment data is required","viaIs.kindness":"VIA-IS assessment data is required","viaIs.socialIntelligence":"VIA-IS assessment data is required","viaIs.teamwork":"VIA-IS assessment data is required","viaIs.fairness":"VIA-IS assessment data is required","viaIs.leadership":"VIA-IS assessment data is required","viaIs.forgiveness":"VIA-IS assessment data is required","viaIs.humility":"VIA-IS assessment data is required","viaIs.prudence":"VIA-IS assessment data is required","viaIs.selfRegulation":"VIA-IS assessment data is required","viaIs.appreciationOfBeauty":"VIA-IS assessment data is required","viaIs.gratitude":"VIA-IS assessment data is required","viaIs.hope":"VIA-IS assessment data is required","viaIs.humor":"VIA-IS assessment data is required","viaIs.spirituality":"VIA-IS assessment data is required"},"userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","url":"/assessments/submit"}
[2025-07-20T01:22:11.696Z] [INFO] ::1 - - [20/Jul/2025:01:22:11 +0000] "POST /assessments/submit HTTP/1.1" 400 1146 "-" "axios/1.10.0"
[2025-07-20T01:22:32.101Z] [INFO] Assessment submission received {"userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-20T01:22:32.134Z] [INFO] Token deduction successful {"userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","deductedAmount":1,"remainingBalance":2}
[2025-07-20T01:22:32.135Z] [INFO] Creating job in Archive Service {"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
[2025-07-20T01:22:32.201Z] [INFO] Job created in Archive Service successfully {"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","archiveJobId":"b21940f6-fe3e-48f0-b3e6-d5c883293463"}
[2025-07-20T01:22:32.201Z] [INFO] Job created {"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","userEmail":"<EMAIL>","status":"queued"}
[2025-07-20T01:22:32.203Z] [INFO] Assessment job published to queue {"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-20T01:22:32.213Z] [INFO] ::1 - - [20/Jul/2025:01:22:32 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-20T01:23:04.457Z] [INFO] Job status updated {"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","status":"completed","progress":100,"error":null}
[2025-07-20T01:23:04.458Z] [INFO] Syncing job status with Archive Service {"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","status":"completed","additionalData":{"result_id":"8c110d96-827c-481a-b5fb-05ddb73e2f75"}}
[2025-07-20T01:23:04.467Z] [INFO] Job status synced successfully with Archive Service {"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","status":"completed"}
[2025-07-20T01:23:04.467Z] [INFO] Job status updated via callback {"jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","resultId":"8c110d96-827c-481a-b5fb-05ddb73e2f75","status":"completed","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
[2025-07-20T01:23:04.468Z] [INFO] ::1 - - [20/Jul/2025:01:23:04 +0000] "POST /assessments/callback/completed HTTP/1.1" 200 191 "-" "axios/1.10.0"
[2025-07-20T01:25:23.103Z] [INFO] Assessment submission received {"userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-20T01:25:23.116Z] [INFO] Token deduction successful {"userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","deductedAmount":1,"remainingBalance":1}
[2025-07-20T01:25:23.116Z] [INFO] Creating job in Archive Service {"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
[2025-07-20T01:25:23.123Z] [INFO] Job created in Archive Service successfully {"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","archiveJobId":"b540bb30-1d95-4f63-828b-1fd96ba1282b"}
[2025-07-20T01:25:23.124Z] [INFO] Job created {"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","userEmail":"<EMAIL>","status":"queued"}
[2025-07-20T01:25:23.124Z] [INFO] Assessment job published to queue {"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-20T01:25:23.125Z] [INFO] ::1 - - [20/Jul/2025:01:25:23 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-20T01:25:55.281Z] [INFO] Job status updated {"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","status":"completed","progress":100,"error":null}
[2025-07-20T01:25:55.282Z] [INFO] Syncing job status with Archive Service {"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","status":"completed","additionalData":{"result_id":"1e80bd86-bf48-486a-ad83-fa12fd86c4f5"}}
[2025-07-20T01:25:55.290Z] [INFO] Job status synced successfully with Archive Service {"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","status":"completed"}
[2025-07-20T01:25:55.290Z] [INFO] Job status updated via callback {"jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","resultId":"1e80bd86-bf48-486a-ad83-fa12fd86c4f5","status":"completed","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
[2025-07-20T01:25:55.291Z] [INFO] ::1 - - [20/Jul/2025:01:25:55 +0000] "POST /assessments/callback/completed HTTP/1.1" 200 191 "-" "axios/1.10.0"
[2025-07-20T01:29:26.419Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-20T01:29:26.448Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-20T01:29:26.448Z] [INFO] Queue service initialized successfully
[2025-07-20T01:29:26.448Z] [INFO] All services initialized successfully
[2025-07-20T01:29:26.451Z] [INFO] Assessment Service running on port 3003
[2025-07-20T01:29:26.451Z] [INFO] Environment: development
[2025-07-20T01:30:08.844Z] [INFO] ::1 - - [20/Jul/2025:01:30:08 +0000] "GET /health HTTP/1.1" 200 590 "-" "axios/1.10.0"
[2025-07-20T01:30:09.113Z] [INFO] Assessment submission received {"userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-20T01:30:09.125Z] [INFO] Token deduction successful {"userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","deductedAmount":1,"remainingBalance":0}
[2025-07-20T01:30:09.125Z] [INFO] Creating job in Archive Service {"jobId":"03e18fa1-6f4e-420d-81fb-16f7eb9cd05c","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
[2025-07-20T01:30:09.152Z] [INFO] Job created in Archive Service successfully {"jobId":"03e18fa1-6f4e-420d-81fb-16f7eb9cd05c","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","archiveJobId":"dc3bae7d-c39e-4c13-bbcc-a743152aec26"}
[2025-07-20T01:30:09.153Z] [INFO] Job created {"jobId":"03e18fa1-6f4e-420d-81fb-16f7eb9cd05c","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","userEmail":"<EMAIL>","status":"queued"}
[2025-07-20T01:30:09.154Z] [INFO] Assessment job published to queue {"jobId":"03e18fa1-6f4e-420d-81fb-16f7eb9cd05c","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-20T01:30:09.161Z] [INFO] ::1 - - [20/Jul/2025:01:30:09 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-20T01:30:31.384Z] [INFO] Job status updated {"jobId":"03e18fa1-6f4e-420d-81fb-16f7eb9cd05c","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","status":"completed","progress":100,"error":null}
[2025-07-20T01:30:31.384Z] [INFO] Syncing job status with Archive Service {"jobId":"03e18fa1-6f4e-420d-81fb-16f7eb9cd05c","status":"completed","additionalData":{"result_id":"e10e90c4-0384-4671-bd5e-00708cbfc3ef"}}
[2025-07-20T01:30:31.394Z] [INFO] Job status synced successfully with Archive Service {"jobId":"03e18fa1-6f4e-420d-81fb-16f7eb9cd05c","status":"completed"}
[2025-07-20T01:30:31.394Z] [INFO] Job status updated via callback {"jobId":"03e18fa1-6f4e-420d-81fb-16f7eb9cd05c","resultId":"e10e90c4-0384-4671-bd5e-00708cbfc3ef","status":"completed","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
[2025-07-20T01:30:31.395Z] [INFO] ::1 - - [20/Jul/2025:01:30:31 +0000] "POST /assessments/callback/completed HTTP/1.1" 200 191 "-" "axios/1.10.0"
[2025-07-20T01:46:11.557Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-20T01:46:11.588Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-20T01:46:11.589Z] [INFO] Queue service initialized successfully
[2025-07-20T01:46:11.589Z] [INFO] All services initialized successfully
[2025-07-20T01:46:11.593Z] [INFO] Assessment Service running on port 3003
[2025-07-20T01:46:11.593Z] [INFO] Environment: development
[2025-07-20T01:46:26.963Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-20T01:46:26.990Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-20T01:46:26.990Z] [INFO] Queue service initialized successfully
[2025-07-20T01:46:26.991Z] [INFO] All services initialized successfully
[2025-07-20T01:46:59.527Z] [INFO] ::1 - - [20/Jul/2025:01:46:59 +0000] "GET /health HTTP/1.1" 200 589 "-" "axios/1.10.0"
[2025-07-20T01:47:31.355Z] [INFO] ::1 - - [20/Jul/2025:01:47:31 +0000] "GET /health HTTP/1.1" 200 588 "-" "axios/1.10.0"
[2025-07-20T01:47:31.604Z] [WARN] Insufficient token balance {"userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","email":"<EMAIL>","currentBalance":0,"requiredTokens":1,"url":"/assessments/submit"}
[2025-07-20T01:47:31.605Z] [INFO] ::1 - - [20/Jul/2025:01:47:31 +0000] "POST /assessments/submit HTTP/1.1" 402 161 "-" "axios/1.10.0"
[2025-07-20T01:47:58.364Z] [INFO] ::1 - - [20/Jul/2025:01:47:58 +0000] "GET /health HTTP/1.1" 200 590 "-" "axios/1.10.0"
[2025-07-20T01:47:58.538Z] [INFO] Assessment submission received {"userId":"8dd66441-da32-47ba-b532-d4fd25b82162","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-20T01:47:58.547Z] [INFO] Token deduction successful {"userId":"8dd66441-da32-47ba-b532-d4fd25b82162","deductedAmount":1,"remainingBalance":2}
[2025-07-20T01:47:58.548Z] [INFO] Creating job in Archive Service {"jobId":"********-d3a4-4e5f-9eff-5ebda8140e5c","userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
[2025-07-20T01:47:58.575Z] [INFO] Job created in Archive Service successfully {"jobId":"********-d3a4-4e5f-9eff-5ebda8140e5c","userId":"8dd66441-da32-47ba-b532-d4fd25b82162","archiveJobId":"6a16de22-c404-4099-bf7a-c81166d470ef"}
[2025-07-20T01:47:58.576Z] [INFO] Job created {"jobId":"********-d3a4-4e5f-9eff-5ebda8140e5c","userId":"8dd66441-da32-47ba-b532-d4fd25b82162","userEmail":"<EMAIL>","status":"queued"}
[2025-07-20T01:47:58.577Z] [INFO] Assessment job published to queue {"jobId":"********-d3a4-4e5f-9eff-5ebda8140e5c","userId":"8dd66441-da32-47ba-b532-d4fd25b82162","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-20T01:47:58.578Z] [INFO] ::1 - - [20/Jul/2025:01:47:58 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-20T01:48:20.813Z] [INFO] Job status updated {"jobId":"********-d3a4-4e5f-9eff-5ebda8140e5c","userId":"8dd66441-da32-47ba-b532-d4fd25b82162","status":"completed","progress":100,"error":null}
[2025-07-20T01:48:20.813Z] [INFO] Syncing job status with Archive Service {"jobId":"********-d3a4-4e5f-9eff-5ebda8140e5c","status":"completed","additionalData":{"result_id":"62c0151b-d7d9-453c-99a8-92b1438b69aa"}}
[2025-07-20T01:48:20.823Z] [INFO] Job status synced successfully with Archive Service {"jobId":"********-d3a4-4e5f-9eff-5ebda8140e5c","status":"completed"}
[2025-07-20T01:48:20.823Z] [INFO] Job status updated via callback {"jobId":"********-d3a4-4e5f-9eff-5ebda8140e5c","resultId":"62c0151b-d7d9-453c-99a8-92b1438b69aa","status":"completed","userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
[2025-07-20T01:48:20.825Z] [INFO] ::1 - - [20/Jul/2025:01:48:20 +0000] "POST /assessments/callback/completed HTTP/1.1" 200 191 "-" "axios/1.10.0"
[2025-07-20T01:49:36.672Z] [INFO] ::1 - - [20/Jul/2025:01:49:36 +0000] "GET /health HTTP/1.1" 200 591 "-" "axios/1.10.0"
[2025-07-20T01:49:36.826Z] [INFO] Assessment submission received {"userId":"8dd66441-da32-47ba-b532-d4fd25b82162","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-20T01:49:36.835Z] [INFO] Token deduction successful {"userId":"8dd66441-da32-47ba-b532-d4fd25b82162","deductedAmount":1,"remainingBalance":1}
[2025-07-20T01:49:36.835Z] [INFO] Creating job in Archive Service {"jobId":"0916ea8b-0d8b-423e-b0a3-ac9734f3b1ec","userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
[2025-07-20T01:49:36.841Z] [INFO] Job created in Archive Service successfully {"jobId":"0916ea8b-0d8b-423e-b0a3-ac9734f3b1ec","userId":"8dd66441-da32-47ba-b532-d4fd25b82162","archiveJobId":"c97f8402-8c7d-494c-869c-c4aafc296dc7"}
[2025-07-20T01:49:36.842Z] [INFO] Job created {"jobId":"0916ea8b-0d8b-423e-b0a3-ac9734f3b1ec","userId":"8dd66441-da32-47ba-b532-d4fd25b82162","userEmail":"<EMAIL>","status":"queued"}
[2025-07-20T01:49:36.842Z] [INFO] Assessment job published to queue {"jobId":"0916ea8b-0d8b-423e-b0a3-ac9734f3b1ec","userId":"8dd66441-da32-47ba-b532-d4fd25b82162","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-20T01:49:36.844Z] [INFO] ::1 - - [20/Jul/2025:01:49:36 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-20T01:49:54.450Z] [INFO] Job status updated {"jobId":"0916ea8b-0d8b-423e-b0a3-ac9734f3b1ec","userId":"8dd66441-da32-47ba-b532-d4fd25b82162","status":"completed","progress":100,"error":null}
[2025-07-20T01:49:54.452Z] [INFO] Syncing job status with Archive Service {"jobId":"0916ea8b-0d8b-423e-b0a3-ac9734f3b1ec","status":"completed","additionalData":{"result_id":"4c51ca81-b2f4-49f8-858c-fb9bb6c77191"}}
[2025-07-20T01:49:54.460Z] [INFO] Job status synced successfully with Archive Service {"jobId":"0916ea8b-0d8b-423e-b0a3-ac9734f3b1ec","status":"completed"}
[2025-07-20T01:49:54.460Z] [INFO] Job status updated via callback {"jobId":"0916ea8b-0d8b-423e-b0a3-ac9734f3b1ec","resultId":"4c51ca81-b2f4-49f8-858c-fb9bb6c77191","status":"completed","userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
[2025-07-20T01:49:54.461Z] [INFO] ::1 - - [20/Jul/2025:01:49:54 +0000] "POST /assessments/callback/completed HTTP/1.1" 200 191 "-" "axios/1.10.0"
[2025-07-20T01:58:18.124Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-20T01:58:18.157Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-20T01:58:18.158Z] [INFO] Queue service initialized successfully
[2025-07-20T01:58:18.158Z] [INFO] All services initialized successfully
[2025-07-20T01:58:18.162Z] [INFO] Assessment Service running on port 3003
[2025-07-20T01:58:18.162Z] [INFO] Environment: development
[2025-07-20T02:06:14.334Z] [INFO] ::1 - - [20/Jul/2025:02:06:14 +0000] "GET /health HTTP/1.1" 200 591 "-" "axios/1.10.0"
[2025-07-20T02:06:14.627Z] [INFO] Assessment submission received {"userId":"fa7b0620-901b-4c0c-859d-adede356a3dd","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-20T02:06:14.639Z] [INFO] Token deduction successful {"userId":"fa7b0620-901b-4c0c-859d-adede356a3dd","deductedAmount":1,"remainingBalance":2}
[2025-07-20T02:06:14.640Z] [INFO] Creating job in Archive Service {"jobId":"382425b7-f627-4432-b9c5-6db72a07ec48","userId":"fa7b0620-901b-4c0c-859d-adede356a3dd"}
[2025-07-20T02:06:14.667Z] [INFO] Job created in Archive Service successfully {"jobId":"382425b7-f627-4432-b9c5-6db72a07ec48","userId":"fa7b0620-901b-4c0c-859d-adede356a3dd","archiveJobId":"a4c312cd-759c-44cf-8ee2-c7bb8072b3c6"}
[2025-07-20T02:06:14.667Z] [INFO] Job created {"jobId":"382425b7-f627-4432-b9c5-6db72a07ec48","userId":"fa7b0620-901b-4c0c-859d-adede356a3dd","userEmail":"<EMAIL>","status":"queued"}
[2025-07-20T02:06:14.668Z] [INFO] Assessment job published to queue {"jobId":"382425b7-f627-4432-b9c5-6db72a07ec48","userId":"fa7b0620-901b-4c0c-859d-adede356a3dd","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-20T02:06:14.670Z] [INFO] ::1 - - [20/Jul/2025:02:06:14 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-20T02:06:31.892Z] [INFO] Job status updated {"jobId":"382425b7-f627-4432-b9c5-6db72a07ec48","userId":"fa7b0620-901b-4c0c-859d-adede356a3dd","status":"completed","progress":100,"error":null}
[2025-07-20T02:06:31.892Z] [INFO] Syncing job status with Archive Service {"jobId":"382425b7-f627-4432-b9c5-6db72a07ec48","status":"completed","additionalData":{"result_id":"a81484e4-29a0-45d9-8560-ee59f635200c"}}
[2025-07-20T02:06:31.901Z] [INFO] Job status synced successfully with Archive Service {"jobId":"382425b7-f627-4432-b9c5-6db72a07ec48","status":"completed"}
[2025-07-20T02:06:31.901Z] [INFO] Job status updated via callback {"jobId":"382425b7-f627-4432-b9c5-6db72a07ec48","resultId":"a81484e4-29a0-45d9-8560-ee59f635200c","status":"completed","userId":"fa7b0620-901b-4c0c-859d-adede356a3dd"}
[2025-07-20T02:06:31.902Z] [INFO] ::1 - - [20/Jul/2025:02:06:31 +0000] "POST /assessments/callback/completed HTTP/1.1" 200 191 "-" "axios/1.10.0"
[2025-07-20T02:09:10.232Z] [INFO] ::1 - - [20/Jul/2025:02:09:10 +0000] "GET /health HTTP/1.1" 200 591 "-" "axios/1.10.0"
[2025-07-20T02:09:10.413Z] [INFO] Assessment submission received {"userId":"d664b357-1ad1-4b24-a08b-a39d2f2cfcb6","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-20T02:09:10.425Z] [INFO] Token deduction successful {"userId":"d664b357-1ad1-4b24-a08b-a39d2f2cfcb6","deductedAmount":1,"remainingBalance":2}
[2025-07-20T02:09:10.425Z] [INFO] Creating job in Archive Service {"jobId":"********-06b7-4461-918d-6aeaff69ef64","userId":"d664b357-1ad1-4b24-a08b-a39d2f2cfcb6"}
[2025-07-20T02:09:10.432Z] [INFO] Job created in Archive Service successfully {"jobId":"********-06b7-4461-918d-6aeaff69ef64","userId":"d664b357-1ad1-4b24-a08b-a39d2f2cfcb6","archiveJobId":"4abb0032-af87-4c0e-b73c-80bd39e08202"}
[2025-07-20T02:09:10.432Z] [INFO] Job created {"jobId":"********-06b7-4461-918d-6aeaff69ef64","userId":"d664b357-1ad1-4b24-a08b-a39d2f2cfcb6","userEmail":"<EMAIL>","status":"queued"}
[2025-07-20T02:09:10.432Z] [INFO] Assessment job published to queue {"jobId":"********-06b7-4461-918d-6aeaff69ef64","userId":"d664b357-1ad1-4b24-a08b-a39d2f2cfcb6","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-20T02:09:10.434Z] [INFO] ::1 - - [20/Jul/2025:02:09:10 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-20T02:09:32.858Z] [INFO] Job status updated {"jobId":"********-06b7-4461-918d-6aeaff69ef64","userId":"d664b357-1ad1-4b24-a08b-a39d2f2cfcb6","status":"completed","progress":100,"error":null}
[2025-07-20T02:09:32.859Z] [INFO] Syncing job status with Archive Service {"jobId":"********-06b7-4461-918d-6aeaff69ef64","status":"completed","additionalData":{"result_id":"2703a3d1-53b2-487e-8e99-ccccfdc3fd03"}}
[2025-07-20T02:09:32.870Z] [INFO] Job status synced successfully with Archive Service {"jobId":"********-06b7-4461-918d-6aeaff69ef64","status":"completed"}
[2025-07-20T02:09:32.870Z] [INFO] Job status updated via callback {"jobId":"********-06b7-4461-918d-6aeaff69ef64","resultId":"2703a3d1-53b2-487e-8e99-ccccfdc3fd03","status":"completed","userId":"d664b357-1ad1-4b24-a08b-a39d2f2cfcb6"}
[2025-07-20T02:09:32.871Z] [INFO] ::1 - - [20/Jul/2025:02:09:32 +0000] "POST /assessments/callback/completed HTTP/1.1" 200 191 "-" "axios/1.10.0"
